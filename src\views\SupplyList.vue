<template>
  <div class="supply-list-page">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">供应列表管理</h2>
        <p class="page-subtitle">高效管理和查看所有供应信息</p>
      </div>
      <div class="header-right">
        <el-card class="total-supply-card" shadow="never">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalSupplies }}</div>
              <div class="stat-label">总供应数</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>





    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题、内容、联系人..."
            clearable
            style="width: 300px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="searchLoading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleRefresh">
            <el-icon><RefreshRight /></el-icon>
            刷新数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span class="table-title">供应数据列表</span>
          <div class="table-actions">
            <el-tooltip content="导出数据">
              <el-button type="info" size="small" @click="handleExport">
                <el-icon><Download /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="列设置">
              <el-button type="info" size="small" @click="handleColumnSetting">
                <el-icon><Setting /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :height="tableHeight"
        :row-style="{ height: '80px' }"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" fixed="left" />
        <el-table-column label="供应信息" min-width="320" fixed="left">
          <template #default="{ row }">
            <div class="supply-info-cell">
              <div class="image-section">
                <div class="images-gallery" v-if="getAllImages(row).length > 0">
                  <el-image
                    v-for="(image, index) in getAllImages(row)"
                    :key="index"
                    :src="image"
                    style="width: 60px; height: 60px; margin-right: 8px;"
                    fit="cover"
                    :preview-src-list="getAllImages(row)"
                    :initial-index="index"
                    preview-teleported
                    class="preview-image"
                    @error="handleImageError"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>失败</span>
                      </div>
                    </template>
                    <template #placeholder>
                      <div class="image-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div v-else class="no-image-placeholder">
                  <el-icon><Picture /></el-icon>
                  <span>暂无图片</span>
                </div>
              </div>
              <div class="info-section">
                <div class="title-row">
                  <h4 class="supply-title">{{ row.title }}</h4>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="价格信息" width="160" sortable="custom">
          <template #default="{ row }">
            <div class="price-cell">
              <div class="price-main">
                <span class="price-value">¥{{ formatPrice(row.price) }}</span>
                <span class="price-unit">/{{ row.price_unit || '株' }}</span>
              </div>
              <div class="price-local" v-if="row.localPrice && row.localPrice !== row.price">
                <span class="local-label">本地价:</span>
                <span class="local-price">¥{{ formatPrice(row.localPrice) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系方式" width="160">
          <template #default="{ row }">
            <div class="contact-cell">
              <div class="contact-phone" v-if="row.phoneNumber">
                <el-icon><Phone /></el-icon>
                <span>{{ row.phoneNumber }}</span>
              </div>
              <div class="contact-name" v-if="row.contactName">
                {{ row.contactName }}
              </div>
            </div>
          </template>
        </el-table-column>



        <el-table-column label="发布时间" width="140" sortable="custom">
          <template #default="{ row }">
            <div class="time-cell">
              <div class="time-main">{{ formatDate(row.createTime) }}</div>
              <div class="time-sub">{{ formatTimeOnly(row.createTime) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button type="primary" size="small" circle @click="handleView(row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑信息" placement="top">
                <el-button type="warning" size="small" circle @click="handleEdit(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除记录" placement="top">
                <el-button type="danger" size="small" circle @click="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 高级分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>共 {{ pagination.total }} 条记录，第 {{ pagination.currentPage }} / {{ totalPages }} 页</span>
          <span v-if="selectedRows.length > 0" class="selection-info">
            已选择 {{ selectedRows.length }} 条记录
          </span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          layout="prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          class="pagination-component"
        />
      </div>
    </el-card>

    <!-- 详情查看弹窗 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="供应详情"
      width="80%"
      :destroy-on-close="true"
      class="view-dialog"
    >
      <div v-if="currentRow" class="detail-content">
        <el-row :gutter="24">
          <el-col :span="16">
            <div class="detail-section">
              <h3>基本信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="供应标题">{{ currentRow.title }}</el-descriptions-item>
                <el-descriptions-item label="分类">{{ currentRow.category }}</el-descriptions-item>
                <el-descriptions-item label="价格">¥{{ currentRow.price }}/{{ currentRow.price_unit }}</el-descriptions-item>
                <el-descriptions-item label="本地价格">¥{{ currentRow.localPrice }}/{{ currentRow.price_unit }}</el-descriptions-item>
                <el-descriptions-item label="数量">{{ currentRow.quantity }}{{ currentRow.unit }}</el-descriptions-item>
                <el-descriptions-item label="品质">{{ currentRow.growthStage }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section">
              <h3>联系信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="联系人">{{ currentRow.contactName }}</el-descriptions-item>
                <el-descriptions-item label="联系电话">{{ currentRow.contactPhone }}</el-descriptions-item>
                <el-descriptions-item label="地区" :span="2">{{ currentRow.location }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section" v-if="hasSpecifications(currentRow)">
              <h3>规格参数</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="高度" v-if="currentRow.height">{{ currentRow.height }}cm</el-descriptions-item>
                <el-descriptions-item label="米径" v-if="currentRow.trunkDiameter">{{ currentRow.trunkDiameter }}cm</el-descriptions-item>
                <el-descriptions-item label="冠幅" v-if="currentRow.crownWidth">{{ currentRow.crownWidth }}cm</el-descriptions-item>
                <el-descriptions-item label="地径" v-if="currentRow.groundDiameter">{{ currentRow.groundDiameter }}cm</el-descriptions-item>
                <el-descriptions-item label="胸径" v-if="currentRow.chestDiameter">{{ currentRow.chestDiameter }}cm</el-descriptions-item>
                <el-descriptions-item label="苗龄" v-if="currentRow.plantAge">{{ currentRow.plantAge }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section" v-if="currentRow.content">
              <h3>详细描述</h3>
              <div class="content-text">{{ currentRow.content }}</div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="detail-section">
              <h3>产品图片</h3>
              <div class="image-gallery" v-if="getAllImages(currentRow).length > 0">
                <el-image
                  v-for="(img, index) in getAllImages(currentRow)"
                  :key="index"
                  :src="img"
                  style="width: 100px; height: 100px; margin: 5px"
                  fit="cover"
                  :preview-src-list="getAllImages(currentRow)"
                  :initial-index="index"
                  preview-teleported
                />
              </div>
              <div v-else class="no-images">暂无图片</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editMode === 'add' ? '新增供应' : '编辑供应'"
      width="90%"
      :destroy-on-close="true"
      class="edit-dialog"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="120px"
        class="edit-form"
      >
        <el-tabs v-model="activeTab" class="edit-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="供应标题" prop="title">
                  <el-input v-model="editForm.title" placeholder="请输入供应标题" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分类" prop="category">
                  <el-select v-model="editForm.category" placeholder="请选择分类" style="width: 100%">
                    <el-option label="常见" value="常见" />
                    <el-option label="藤本类" value="藤本类" />
                    <el-option label="草皮类" value="草皮类" />
                    <el-option label="花草" value="花草" />
                    <el-option label="种子" value="种子" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="详细描述" prop="content">
              <el-input
                v-model="editForm.content"
                type="textarea"
                rows="4"
                placeholder="请输入详细描述"
              />
            </el-form-item>

            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="价格" prop="price">
                  <el-input v-model="editForm.price" placeholder="请输入价格" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="本地价格" prop="localPrice">
                  <el-input v-model="editForm.localPrice" placeholder="请输入本地价格" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="价格单位" prop="price_unit">
                  <el-select v-model="editForm.price_unit" placeholder="请选择单位" style="width: 100%">
                    <el-option label="棵" value="棵" />
                    <el-option label="株" value="株" />
                    <el-option label="公斤" value="公斤" />
                    <el-option label="斤" value="斤" />
                    <el-option label="平方米" value="平方米" />
                    <el-option label="厘米" value="厘米" />
                    <el-option label="袋" value="袋" />
                    <el-option label="捆" value="捆" />
                    <el-option label="杯" value="杯" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="联系信息" name="contact">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactName">
                  <el-input v-model="editForm.contactName" placeholder="请输入联系人姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input v-model="editForm.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="地区信息" prop="location">
              <el-input v-model="editForm.location" placeholder="请输入详细地区信息" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="产品参数" name="params">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="作物品种">
                  <el-input v-model="editForm.cropVariety" placeholder="请输入作物品种" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="品质等级">
                  <el-select v-model="editForm.growthStage" placeholder="请选择品质" style="width: 100%">
                    <el-option label="精品" value="精品" />
                    <el-option label="中等" value="中等" />
                    <el-option label="一般" value="一般" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="栽培状态">
                  <el-select v-model="editForm.plant_method" placeholder="请选择栽培状态" style="width: 100%">
                    <el-option label="地栽苗" value="地栽苗" />
                    <el-option label="小杯苗" value="小杯苗" />
                    <el-option label="大杯苗" value="大杯苗" />
                    <el-option label="袋装苗" value="袋装苗" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="数量">
                  <el-input v-model="editForm.quantity" placeholder="请输入数量" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数量单位">
                  <el-select v-model="editForm.unit" placeholder="请选择单位" style="width: 100%">
                    <el-option label="株" value="株" />
                    <el-option label="盆" value="盆" />
                    <el-option label="斤" value="斤" />
                    <el-option label="公斤" value="公斤" />
                    <el-option label="箱" value="箱" />
                    <el-option label="件" value="件" />
                    <el-option label="包" value="包" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <div class="spec-section">
              <h4>规格参数（可选）</h4>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="高度(cm)">
                    <el-input v-model="editForm.height" placeholder="高度" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="米径(cm)">
                    <el-input v-model="editForm.trunkDiameter" placeholder="米径" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="冠幅(cm)">
                    <el-input v-model="editForm.crownWidth" placeholder="冠幅" type="number" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="地径(cm)">
                    <el-input v-model="editForm.groundDiameter" placeholder="地径" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="胸径(cm)">
                    <el-input v-model="editForm.chestDiameter" placeholder="胸径" type="number" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="苗龄">
                    <el-input v-model="editForm.plantAge" placeholder="苗龄" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <el-tab-pane label="图片管理" name="images">
            <div class="image-upload-section">
              <el-upload
                class="image-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                multiple
              >
                <el-button type="primary">
                  <el-icon><Plus /></el-icon>
                  上传图片
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png格式，单个文件不超过5MB，最多上传9张图片
                  </div>
                </template>
              </el-upload>

              <div class="image-list" v-if="editForm.imageList && editForm.imageList.length > 0">
                <div
                  v-for="(image, index) in editForm.imageList"
                  :key="index"
                  class="image-item"
                >
                  <el-image
                    :src="image"
                    style="width: 100px; height: 100px"
                    fit="cover"
                  />
                  <div class="image-actions">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeImage(index)"
                      circle
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            {{ editMode === 'add' ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getSupplyList, 
  getSupplyStats, 
  createSupply, 
  updateSupply, 
  deleteSupply, 
  batchDeleteSupplies,
  getSupplyDetail,
  getCategoryStats
} from '@/api/supply'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const saveLoading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const currentRow = ref(null)

// 弹窗状态
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const editMode = ref('edit') // 'add' | 'edit'
const activeTab = ref('basic')

// 表单引用
const editFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序配置
const sortConfig = reactive({
  prop: '',
  order: ''
})

// 搜索防抖处理
const searchDebounce = ref(null)

// 编辑表单
const editForm = reactive({
  id: null,
  title: '',
  content: '',
  price: '',
  localPrice: '',
  price_unit: '棵',
  category: '',
  contactName: '',
  contactPhone: '',
  location: '',
  cropVariety: '',
  growthStage: '',
  quantity: '',
  unit: '株',
  plant_method: '',
  height: '',
  trunkDiameter: '',
  crownWidth: '',
  groundDiameter: '',
  chestDiameter: '',
  plantAge: '',
  imageList: [],
  newImageList: []
})

// 表单验证规则
const editRules = {
  title: [
    { required: true, message: '请输入供应标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  price_unit: [
    { required: true, message: '请选择价格单位', trigger: 'change' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入地区信息', trigger: 'blur' }
  ]
}

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(pagination.total / pagination.pageSize)
})

const tableHeight = computed(() => {
  return window.innerHeight - 360
})

// 统计数据
const totalSupplies = ref(0)

// 获取数据的核心方法 - 企业级缓存策略
const loadSupplyData = async (useCache = true) => {
  loading.value = true
  try {
    // 调用真实API
    const response = await getSupplyList({
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      sortBy: sortConfig.prop,
      sortOrder: sortConfig.order
    }, useCache)

    // 检查响应格式
    if (response && response.data) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
      
      // 更新统计数据
      updateStats(tableData.value)
    } else {
      throw new Error('数据格式错误')
    }
  } catch (error) {
    ElMessage.error('数据加载失败：' + error.message)
    console.error('Load data error:', error)
    // 清空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getSupplyStats()
    if (response && response.data) {
      const stats = response.data
      totalSupplies.value = stats.total || 0
    }
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

// 更新统计数据
const updateStats = async (data) => {
  // 加载实时统计数据而不是从当前页面数据计算
  await loadStatsData()
}

// 动态获取API基础URL
const getImageApiBaseUrl = () => {
  // 获取当前访问的主机名
  const hostname = window.location.hostname
  const protocol = window.location.protocol

  // 如果是IP地址（局域网访问），使用相同的IP访问后端
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return `${protocol}//${hostname}:3000`
  }

  // 如果是localhost或其他域名，使用localhost
  return 'http://localhost:3000'
}

// 处理图片URL，确保格式正确
const processImageUrl = (url) => {
  if (!url) return ''

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果是data URL，直接返回
  if (url.startsWith('data:')) {
    return url
  }

  // 处理腾讯云存储路径 cloud://
  if (url.startsWith('cloud://')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?path=${encodeURIComponent(url)}`
  }

  // 如果是相对路径，直接返回
  if (url.startsWith('./') || url.startsWith('../')) {
    return url
  }

  // 如果是云存储文件名（包含 imageNew_ 或时间戳）
  if (url.includes('imageNew_') || url.includes('_')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?filename=${encodeURIComponent(url)}`
  }

  // 普通文件名，拼接uploads路径
  const baseUrl = getImageApiBaseUrl()
  return `${baseUrl}/uploads/${url}`
}

// 图片处理函数
const getFirstImage = (row) => {
  if (row.newImageList && row.newImageList.length > 0) {
    const url = row.newImageList[0].url || row.newImageList[0]
    return processImageUrl(url)
  }
  if (row.imageList && row.imageList.length > 0) {
    return processImageUrl(row.imageList[0])
  }
  return null
}

const getAllImages = (row) => {
  const images = []
  if (row.newImageList && row.newImageList.length > 0) {
    images.push(...row.newImageList.map(img => processImageUrl(img.url || img)))
  }
  if (row.imageList && row.imageList.length > 0) {
    images.push(...row.imageList.map(url => processImageUrl(url)))
  }
  return images
}

// 获取图片总数
const getImageCount = (row) => {
  let count = 0
  if (row.newImageList && row.newImageList.length > 0) {
    count += row.newImageList.length
  }
  if (row.imageList && row.imageList.length > 0) {
    count += row.imageList.length
  }
  return count
}

// 图片错误处理
const handleImageError = (error) => {
  console.error('图片加载失败:', error)
  console.log('当前图片URL处理情况:')

  // 输出调试信息
  if (tableData.value.length > 0) {
    tableData.value.slice(0, 3).forEach((row, index) => {
      console.log(`供应项 ${index + 1}:`)
      if (row.imageList) {
        console.log('  imageList:', row.imageList)
        row.imageList.forEach((url, i) => {
          console.log(`    图片 ${i}:`, url, '→', processImageUrl(url))
        })
      }
      if (row.newImageList) {
        console.log('  newImageList:', row.newImageList)
        row.newImageList.forEach((image, i) => {
          console.log(`    新图片 ${i}:`, image.url, '→', processImageUrl(image.url))
        })
      }
    })
  }
}



// 检查是否有规格参数
const hasSpecifications = (row) => {
  return row.height || row.trunkDiameter || row.crownWidth || 
         row.groundDiameter || row.chestDiameter || row.plantAge
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN')
}

// 格式化日期（只显示日期部分）
const formatDate = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleDateString('zh-CN')
}

// 格式化时间（只显示时间部分）
const formatTimeOnly = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0'
  return Number(price).toLocaleString('zh-CN')
}



// 事件处理方法
const handleSearch = async () => {
  searchLoading.value = true
  pagination.currentPage = 1
  await loadSupplyData(false)
  searchLoading.value = false
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: ''
  })
  handleSearch()
}

const handleRefresh = () => {
  loadSupplyData(false)
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSortChange = ({ prop, order }) => {
  sortConfig.prop = prop
  sortConfig.order = order
  loadSupplyData(false)
}



const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadSupplyData()
}

// 操作方法
const handleView = (row) => {
  currentRow.value = row
  viewDialogVisible.value = true
}

const handleEdit = (row) => {
  editMode.value = 'edit'
  Object.assign(editForm, { ...row })
  editDialogVisible.value = true
  activeTab.value = 'basic'
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应"${row.title}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true
    try {
      await deleteSupply(row._id || row.id)
      ElMessage.success('删除成功')
      // 重新加载数据
      await loadSupplyData(false)
    } catch (error) {
      ElMessage.error('删除失败：' + error.message)
    } finally {
      loading.value = false
    }
  } catch {
    // 用户取消
  }
}

const handleAdd = () => {
  editMode.value = 'add'
  Object.assign(editForm, {
    id: null,
    title: '',
    content: '',
    price: '',
    localPrice: '',
    price_unit: '棵',
    category: '',
    contactName: '',
    contactPhone: '',
    location: '',
    cropVariety: '',
    growthStage: '',
    quantity: '',
    unit: '株',
    plant_method: '',
    height: '',
    trunkDiameter: '',
    crownWidth: '',
    groundDiameter: '',
    chestDiameter: '',
    plantAge: '',
    imageList: [],
    newImageList: []
  })
  editDialogVisible.value = true
  activeTab.value = 'basic'
}

const handleSave = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    saveLoading.value = true

    try {
      if (editMode.value === 'add') {
        await createSupply(editForm)
        ElMessage.success('创建成功')
      } else {
        await updateSupply(editForm._id || editForm.id, editForm)
        ElMessage.success('保存成功')
      }
      
      editDialogVisible.value = false
      // 重新加载数据
      await loadSupplyData(false)
    } catch (error) {
      ElMessage.error('保存失败：' + error.message)
    } finally {
      saveLoading.value = false
    }
  } catch {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 图片上传处理
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！')
    return false
  }
  
  // 模拟图片上传
  const reader = new FileReader()
  reader.onload = (e) => {
    editForm.imageList.push(e.target.result)
  }
  reader.readAsDataURL(file)
  
  return false // 阻止自动上传
}

const removeImage = (index) => {
  editForm.imageList.splice(index, 1)
}

// 其他功能
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleColumnSetting = () => {
  ElMessage.info('列设置功能开发中...')
}

// 页面加载
onMounted(async () => {
  // 调试：输出图片处理信息
  console.log('图片API基础URL:', getImageApiBaseUrl())
  console.log('测试图片URL处理:')
  console.log('  cloud://test → ', processImageUrl('cloud://test'))
  console.log('  imageNew_123.jpg → ', processImageUrl('imageNew_123.jpg'))
  console.log('  normal.jpg → ', processImageUrl('normal.jpg'))

  // 并行加载数据和统计信息
  await Promise.all([
    loadSupplyData(),
    loadStatsData()
  ])
})
</script>

<style scoped>
.supply-list-page {
  min-height: 100%;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  flex-shrink: 0;
}

.total-supply-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.total-supply-card :deep(.el-card__body) {
  padding: 16px 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.search-card {
  margin-bottom: 12px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}



.stat-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 总供应数卡片特定样式 */
.total-supply-card .stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-supply-card .stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.total-supply-card .stat-value {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 2px;
}

.total-supply-card .stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.table-card {
  margin-top: -4px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.title-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.category-tag {
  align-self: flex-start;
}

.image-preview {
  display: flex;
  justify-content: center;
  position: relative;
}

.preview-image {
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.preview-image:hover {
  transform: scale(1.05);
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

/* 供应信息单元格样式 */
.supply-info-cell {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 8px 0;
}

.image-section {
  flex-shrink: 0;
}

.images-gallery {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  overflow-x: auto;
  max-width: 300px;
  padding: 2px 0;
}

.images-gallery::-webkit-scrollbar {
  height: 4px;
}

.images-gallery::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.images-gallery::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.images-gallery::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.no-image-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
  gap: 4px;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #f56c6c;
  font-size: 10px;
  gap: 2px;
  background-color: #fef0f0;
  border-radius: 6px;
}

.image-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  background-color: #f0f9ff;
  border-radius: 6px;
}

.image-count-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  font-weight: 500;
}

.info-section {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.title-row {
  display: flex;
  align-items: center;
}

.supply-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 价格单元格样式 */
.price-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price-value {
  font-weight: 600;
  color: #e6a23c;
  font-size: 16px;
}

.price-unit {
  font-size: 12px;
  color: #909399;
}

.price-local {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.local-label {
  color: #909399;
}

.local-price {
  color: #67c23a;
  font-weight: 500;
}

/* 联系方式单元格样式 */
.contact-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-phone {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #409eff;
}

.contact-name {
  font-size: 12px;
  color: #909399;
}



/* 时间单元格样式 */
.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.time-sub {
  font-size: 11px;
  color: #909399;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
}

.action-buttons .el-button {
  width: 32px;
  height: 32px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .supply-info-cell {
    gap: 12px;
    padding: 10px 0;
  }

  .supply-title {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .supply-info-cell {
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
  }

  .image-section {
    align-self: center;
  }

  .images-gallery {
    max-width: 100%;
    justify-content: flex-start;
  }

  .supply-title {
    font-size: 14px;
    text-align: center;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 28px;
    height: 28px;
  }

  /* 移动端图片保持70px */
  .no-image-placeholder {
    width: 70px;
    height: 70px;
  }

  /* 移动端el-image也要调整 */
  .supply-info-cell .el-image {
    width: 70px !important;
    height: 70px !important;
  }
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f8f9fa !important;
}

/* 现代表格样式 */
.modern-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none;
}

:deep(.modern-table .el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.modern-table .el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

:deep(.modern-table .el-table th) {
  background-color: #f8fafc;
  color: #475569;
  font-weight: 600;
  font-size: 13px;
  border: none;
  padding: 16px 12px;
}

:deep(.modern-table .el-table td) {
  border: none;
  padding: 12px;
  vertical-align: top;
}

:deep(.modern-table .el-table__row) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.modern-table .el-table__row:last-child) {
  border-bottom: none;
}

:deep(.modern-table .el-table--striped .el-table__row--striped) {
  background-color: #f8fafc;
}

:deep(.modern-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
}

:deep(.modern-table .el-table__fixed-left) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.selection-info {
  color: #409eff;
  font-weight: 500;
}

.pagination-component {
  flex-shrink: 0;
}

.view-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.content-text {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-images {
  text-align: center;
  color: #c0c4cc;
  padding: 40px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.edit-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.edit-tabs {
  margin-top: 16px;
}

.edit-form {
  margin-top: 20px;
}

.spec-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.spec-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.image-upload-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-actions {
  position: absolute;
  top: 4px;
  right: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    align-self: stretch;
  }

  .page-title {
    font-size: 24px;
  }
  

  
  .search-form :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 0;
  }
  
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pagination-info {
    text-align: center;
    order: 2;
  }
  
  .pagination-component {
    order: 1;
  }
  
  .pagination-component :deep(.el-pagination) {
    justify-content: center;
  }
}

/* 深色模式适配 */
:global(.dark) .supply-list-page {
  background-color: #141414;
}

:global(.dark) .page-title {
  color: #fff;
}

:global(.dark) .page-subtitle {
  color: #8c8c8c;
}

:global(.dark) .stat-value {
  color: #fff;
}

:global(.dark) .table-title {
  color: #fff;
}

:global(.dark) .content-text {
  background-color: #262626;
  color: #d9d9d9;
}

:global(.dark) .image-upload-section {
  background-color: #262626;
}
</style>