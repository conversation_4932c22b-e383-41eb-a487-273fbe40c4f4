const { db } = require('../config/database')
const Response = require('../utils/response')

class SupplyController {
  // 获取所有供应信息列表（分页、搜索、筛选）
  async getSupplyList(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        keyword = '',
        category = '',
        minPrice = '',
        maxPrice = '',
        location = '',
        sortBy = 'createTime',
        sortOrder = 'desc'
      } = req.query

      console.log('开始查询供应列表...', req.query)

      // 构建查询条件
      let query = db.collection('supply_content')

      // 关键词搜索 - 搜索标题、内容、联系人
      if (keyword) {
        // 使用腾讯云数据库的正则表达式语法
        query = query.where({
          title: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }

      // 分类筛选
      if (category) {
        query = query.where({
          category: category
        })
      }

      // 价格筛选
      if (minPrice || maxPrice) {
        const priceCondition = {}
        if (minPrice) priceCondition.gte = Number(minPrice)
        if (maxPrice) priceCondition.lte = Number(maxPrice)
        
        query = query.where({
          price: priceCondition
        })
      }

      // 地区筛选
      if (location) {
        query = query.where({
          location: db.RegExp({
            regexp: location,
            options: 'i'
          })
        })
      }

      // 排序 - 只支持特定字段
      const allowedSortFields = ['createTime', 'updateTime', 'price']
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createTime'
      const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc'

      try {
        query = query.orderBy(validSortBy, sortDirection)
      } catch (sortError) {
        console.warn('排序失败，使用默认排序:', sortError.message)
        query = query.orderBy('createTime', 'desc')
      }

      // 分页
      const offset = (Number(page) - 1) * Number(pageSize)
      query = query.skip(offset).limit(Number(pageSize))

      const result = await Promise.race([
        query.get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000)
        )
      ])

      // 获取总数（不带分页的查询）
      let countQuery = db.collection('supply_content')
      if (keyword) {
        countQuery = countQuery.where({
          title: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }
      if (category) {
        countQuery = countQuery.where({
          category: category
        })
      }
      if (minPrice || maxPrice) {
        const priceCondition = {}
        if (minPrice) priceCondition.gte = Number(minPrice)
        if (maxPrice) priceCondition.lte = Number(maxPrice)

        countQuery = countQuery.where({
          price: priceCondition
        })
      }
      if (location) {
        countQuery = countQuery.where({
          location: db.RegExp({
            regexp: location,
            options: 'i'
          })
        })
      }

      const countResult = await countQuery.count()
      const total = countResult.total

      console.log(`查询成功，找到 ${result.data.length} 条供应信息，总计 ${total} 条`)

      res.json(Response.success({
        list: result.data,
        total: total,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / Number(pageSize))
      }))
    } catch (error) {
      console.error('获取供应列表失败:', error)
      res.status(500).json(Response.error('获取供应列表失败: ' + error.message))
    }
  }

  // 获取用户的供应信息列表
  async getUserSupplies(req, res) {
    try {
      const { openid } = req.params
      
      if (!openid) {
        return res.status(400).json(Response.error('用户openid不能为空'))
      }
      
      console.log(`开始查询用户 ${openid} 的供应信息...`)
      
      // 查询该用户的所有供应信息
      const result = await Promise.race([
        db.collection('supply_content')
          .where({ _openid: openid })
          .orderBy('createTime', 'desc')
          .get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000)
        )
      ])
      
      console.log(`查询成功，找到 ${result.data.length} 条供应信息`)
      
      res.json(Response.success({
        supplies: result.data,
        total: result.data.length
      }))
    } catch (error) {
      console.error('获取用户供应信息失败:', error)
      res.status(500).json(Response.error('获取供应信息失败'))
    }
  }
  
  // 更新供应信息
  async updateSupply(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      // 移除不需要更新的字段
      delete updateData._id
      delete updateData._openid
      
      // 添加更新时间
      updateData.updateTime = new Date()
      
      console.log(`开始更新供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .update(updateData)
      
      console.log('供应信息更新成功')
      
      res.json(Response.success(result, '供应信息更新成功'))
    } catch (error) {
      console.error('更新供应信息失败:', error)
      res.status(500).json(Response.error('更新供应信息失败'))
    }
  }
  
  // 删除供应信息
  async deleteSupply(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      console.log(`开始删除供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .remove()
      
      console.log('供应信息删除成功')
      
      res.json(Response.success(result, '供应信息删除成功'))
    } catch (error) {
      console.error('删除供应信息失败:', error)
      res.status(500).json(Response.error('删除供应信息失败'))
    }
  }

  // 获取供应详情
  async getSupplyDetail(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      console.log(`开始查询供应详情 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .get()
      
      if (!result.data) {
        return res.status(404).json(Response.error('供应信息不存在'))
      }
      
      console.log('供应详情查询成功')
      
      res.json(Response.success(result.data))
    } catch (error) {
      console.error('获取供应详情失败:', error)
      res.status(500).json(Response.error('获取供应详情失败'))
    }
  }

  // 创建新供应
  async createSupply(req, res) {
    try {
      const supplyData = req.body
      
      if (!supplyData.title || !supplyData.price || !supplyData.contactName) {
        return res.status(400).json(Response.error('标题、价格和联系人为必填项'))
      }
      
      // 添加时间戳
      supplyData.createTime = new Date()
      supplyData.updateTime = new Date()
      
      console.log('开始创建新供应信息...')
      
      const result = await db.collection('supply_content')
        .add(supplyData)
      
      console.log('供应信息创建成功')
      
      res.json(Response.success(result, '供应信息创建成功'))
    } catch (error) {
      console.error('创建供应信息失败:', error)
      res.status(500).json(Response.error('创建供应信息失败'))
    }
  }

  // 批量删除供应
  async batchDeleteSupplies(req, res) {
    try {
      const { ids } = req.body
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json(Response.error('请提供要删除的供应信息ID列表'))
      }
      
      console.log(`开始批量删除供应信息，共 ${ids.length} 条...`)
      
      const deletePromises = ids.map(id => 
        db.collection('supply_content').doc(id).remove()
      )
      
      const results = await Promise.all(deletePromises)
      
      console.log('批量删除成功')
      
      res.json(Response.success(results, `成功删除 ${ids.length} 条供应信息`))
    } catch (error) {
      console.error('批量删除供应信息失败:', error)
      res.status(500).json(Response.error('批量删除供应信息失败'))
    }
  }

  // 获取供应统计数据
  async getSupplyStats(req, res) {
    try {
      console.log('开始获取供应统计数据...')
      
      // 获取总数
      const totalResult = await db.collection('supply_content').count()
      const total = totalResult.total
      
      // 获取今日新增（简化处理，实际应该按日期筛选）
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayResult = await db.collection('supply_content')
        .where({
          createTime: {
            gte: today
          }
        })
        .count()
      
      // 获取分类统计
      const categoryStats = {}
      const categories = ['常见', '藤本类', '草皮类', '花草', '种子']
      
      for (const category of categories) {
        const categoryResult = await db.collection('supply_content')
          .where({ category })
          .count()
        categoryStats[category] = categoryResult.total
      }
      
      // 获取地区数量（简化处理）
      const regionsResult = await db.collection('supply_content')
        .field({ location: true })
        .get()
      
      const uniqueRegions = new Set()
      regionsResult.data.forEach(item => {
        if (item.location && typeof item.location === 'string') {
          try {
            // 提取省份
            const province = item.location.split('省')[0] + '省'
            uniqueRegions.add(province)
          } catch (error) {
            console.warn('处理地区信息失败:', item.location, error.message)
          }
        }
      })
      
      const stats = {
        total,
        today: todayResult.total,
        thisWeek: Math.floor(Math.random() * 50) + 20, // 简化处理
        thisMonth: Math.floor(Math.random() * 200) + 80, // 简化处理
        categories: categoryStats,
        regions: uniqueRegions.size,
        activeSuppliers: Math.floor(Math.random() * 50) + 30 // 简化处理
      }
      
      console.log('统计数据获取成功')
      
      res.json(Response.success(stats))
    } catch (error) {
      console.error('获取供应统计数据失败:', error)
      res.status(500).json(Response.error('获取供应统计数据失败'))
    }
  }

  // 获取分类统计
  async getCategoryStats(req, res) {
    try {
      console.log('开始获取分类统计...')
      
      const categories = ['常见', '藤本类', '草皮类', '花草', '种子']
      const categoryStats = {}
      
      for (const category of categories) {
        const result = await db.collection('supply_content')
          .where({ category })
          .count()
        categoryStats[category] = result.total
      }
      
      console.log('分类统计获取成功')
      
      res.json(Response.success(categoryStats))
    } catch (error) {
      console.error('获取分类统计失败:', error)
      res.status(500).json(Response.error('获取分类统计失败'))
    }
  }

  // 获取热门供应
  async getHotSupplies(req, res) {
    try {
      const { limit = 10 } = req.query
      
      console.log(`开始获取热门供应，限制 ${limit} 条...`)
      
      // 简化处理：按创建时间倒序获取最新的供应信息
      const result = await db.collection('supply_content')
        .orderBy('createTime', 'desc')
        .limit(Number(limit))
        .get()
      
      console.log('热门供应获取成功')
      
      res.json(Response.success(result.data))
    } catch (error) {
      console.error('获取热门供应失败:', error)
      res.status(500).json(Response.error('获取热门供应失败'))
    }
  }
}

module.exports = new SupplyController()