import request from '../utils/request'
import { cacheManager, performanceMonitor, debounce } from '../utils/performance'

// API基础配置
const API_PREFIX = '/api/supply'
const CACHE_PREFIX = 'supply_api_'
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟缓存

/**
 * 生成缓存键
 */
const getCacheKey = (method, params = {}) => {
  return `${CACHE_PREFIX}${method}_${JSON.stringify(params)}`
}

/**
 * 清除相关缓存
 */
const clearRelatedCache = (pattern) => {
  const keys = Array.from(cacheManager.cache.keys())
  keys.forEach(key => {
    if (key.includes(pattern)) {
      cacheManager.delete(key)
    }
  })
}

// ===== 原有API函数（保持兼容性） =====

// 获取用户的供应信息列表
export const getUserSupplies = (openid) => {
  return request({
    url: `${API_PREFIX}/user/${openid}`,
    method: 'get'
  })
}

// 更新供应信息
export const updateSupply = (id, data) => {
  const promise = request({
    url: `${API_PREFIX}/${id}`,
    method: 'put',
    data
  })
  
  // 清除相关缓存
  promise.then(() => {
    clearRelatedCache(`${CACHE_PREFIX}list`)
    cacheManager.delete(getCacheKey('detail', { id }))
  })
  
  return promise
}

// 删除供应信息
export const deleteSupply = (id) => {
  const promise = request({
    url: `${API_PREFIX}/${id}`,
    method: 'delete'
  })
  
  // 清除相关缓存
  promise.then(() => {
    clearRelatedCache(`${CACHE_PREFIX}list`)
    cacheManager.delete(getCacheKey('detail', { id }))
  })
  
  return promise
}

// ===== 新增企业级API函数 =====

/**
 * 获取供应列表（带缓存和优化）
 */
export const getSupplyList = async (params = {}, useCache = true) => {
  const cacheKey = getCacheKey('list', params)
  
  // 尝试从缓存获取
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getSupplyList')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/list`,
      method: 'get',
      params
    })

    // 缓存结果
    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, DEFAULT_CACHE_EXPIRY)
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getSupplyList', params })
    throw error
  }
}

/**
 * 搜索供应信息（防抖处理）
 */
export const searchSupplies = debounce(async (searchParams) => {
  return getSupplyList(searchParams, false) // 搜索不使用缓存
}, 300)

/**
 * 获取供应详情
 */
export const getSupplyDetail = async (id, useCache = true) => {
  const cacheKey = getCacheKey('detail', { id })
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getSupplyDetail')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/detail/${id}`,
      method: 'get'
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, DEFAULT_CACHE_EXPIRY)
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getSupplyDetail', id })
    throw error
  }
}

/**
 * 创建新供应
 */
export const createSupply = async (data) => {
  const timer = performanceMonitor.startTimer('createSupply')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/create`,
      method: 'post',
      data
    })

    // 清除列表缓存
    clearRelatedCache(`${CACHE_PREFIX}list`)

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'createSupply', data })
    throw error
  }
}

/**
 * 批量删除供应
 */
export const batchDeleteSupplies = async (ids) => {
  const timer = performanceMonitor.startTimer('batchDeleteSupplies')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/batch-delete`,
      method: 'post',
      data: { ids }
    })

    // 清除相关缓存
    clearRelatedCache(`${CACHE_PREFIX}list`)
    ids.forEach(id => {
      cacheManager.delete(getCacheKey('detail', { id }))
    })

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'batchDeleteSupplies', ids })
    throw error
  }
}

/**
 * 获取供应统计数据
 */
export const getSupplyStats = async (useCache = true) => {
  const cacheKey = getCacheKey('stats')
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getSupplyStats')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/stats`,
      method: 'get'
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, 2 * 60 * 1000) // 统计数据缓存2分钟
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getSupplyStats' })
    throw error
  }
}

/**
 * 上传图片
 */
export const uploadSupplyImage = async (file, onProgress) => {
  const formData = new FormData()
  formData.append('image', file)
  
  const timer = performanceMonitor.startTimer('uploadSupplyImage')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/upload/image`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'uploadSupplyImage', fileName: file.name })
    throw error
  }
}

/**
 * 批量上传图片
 */
export const batchUploadImages = async (files, onProgress) => {
  const formData = new FormData()
  files.forEach((file, index) => {
    formData.append(`images[${index}]`, file)
  })
  
  const timer = performanceMonitor.startTimer('batchUploadImages')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/upload/batch`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'batchUploadImages', fileCount: files.length })
    throw error
  }
}

/**
 * 导出供应数据
 */
export const exportSupplyData = async (params = {}) => {
  const timer = performanceMonitor.startTimer('exportSupplyData')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/export`,
      method: 'get',
      params,
      responseType: 'blob'
    })

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'exportSupplyData', params })
    throw error
  }
}

/**
 * 获取供应分类统计
 */
export const getCategoryStats = async (useCache = true) => {
  const cacheKey = getCacheKey('category_stats')
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getCategoryStats')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/category-stats`,
      method: 'get'
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, 10 * 60 * 1000) // 分类统计缓存10分钟
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getCategoryStats' })
    throw error
  }
}

/**
 * 获取热门供应
 */
export const getHotSupplies = async (limit = 10, useCache = true) => {
  const cacheKey = getCacheKey('hot_supplies', { limit })
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getHotSupplies')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/hot`,
      method: 'get',
      params: { limit }
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, 5 * 60 * 1000) // 热门数据缓存5分钟
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getHotSupplies', limit })
    throw error
  }
}

/**
 * 预加载数据
 */
export const preloadSupplyData = async (params = {}) => {
  const promises = []
  
  // 预加载第一页数据
  promises.push(getSupplyList({ ...params, page: 1, pageSize: 20 }))
  
  // 预加载统计数据
  promises.push(getSupplyStats())
  
  // 预加载分类统计
  promises.push(getCategoryStats())
  
  // 预加载热门供应
  promises.push(getHotSupplies(5))
  
  try {
    await Promise.allSettled(promises)
  } catch (error) {
    console.warn('Preload supply data failed:', error)
  }
}

/**
 * 清除所有供应相关缓存
 */
export const clearAllSupplyCache = () => {
  clearRelatedCache(CACHE_PREFIX)
}

/**
 * 获取缓存状态信息
 */
export const getSupplyCacheStatus = () => {
  const allKeys = Array.from(cacheManager.cache.keys())
  const supplyKeys = allKeys.filter(key => key.startsWith(CACHE_PREFIX))
  
  return {
    totalCached: supplyKeys.length,
    keys: supplyKeys,
    size: supplyKeys.reduce((size, key) => {
      const value = cacheManager.get(key)
      return size + (value ? JSON.stringify(value).length : 0)
    }, 0)
  }
}

/**
 * 供应API管理类
 */
export class SupplyAPIManager {
  constructor() {
    this.requestQueue = []
    this.processing = false
    this.maxConcurrent = 3
  }

  // 添加请求到队列
  addRequest(requestFn) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ requestFn, resolve, reject })
      this.processQueue()
    })
  }

  // 处理请求队列
  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) {
      return
    }

    this.processing = true
    
    while (this.requestQueue.length > 0) {
      const batch = this.requestQueue.splice(0, this.maxConcurrent)
      
      await Promise.allSettled(
        batch.map(async ({ requestFn, resolve, reject }) => {
          try {
            const result = await requestFn()
            resolve(result)
          } catch (error) {
            reject(error)
          }
        })
      )
    }

    this.processing = false
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      pending: this.requestQueue.length,
      processing: this.processing
    }
  }
}

// 创建API管理器实例
export const supplyAPIManager = new SupplyAPIManager()

// 导出默认配置
export default {
  getUserSupplies,
  getSupplyList,
  getSupplyDetail,
  createSupply,
  updateSupply,
  deleteSupply,
  batchDeleteSupplies,
  getSupplyStats,
  uploadSupplyImage,
  batchUploadImages,
  exportSupplyData,
  getCategoryStats,
  getHotSupplies,
  searchSupplies,
  preloadSupplyData,
  clearAllSupplyCache,
  getSupplyCacheStatus,
  supplyAPIManager
}