import request from '../utils/request'

// 获取仪表盘统计数据
export const getDashboardStats = () => {
  return request.get('/api/dashboard/stats')
}

// 获取所有用户数据 - 支持分页和搜索
export const getAllUsers = (params = {}) => {
  // 确保参数格式与后端一致
  const queryParams = {
    page: params.page || 1,
    pageSize: params.pageSize || 20,
    keyword: params.keyword || '',
    searchType: params.searchType || 'nickName'
  }

  return request.get('/api/dashboard/users', { params: queryParams })
}

// 更新用户信息
export const updateUser = (userData) => {
  return request.put(`/api/dashboard/users/${userData._id}`, userData)
}

// 获取单个用户详情
export const getUserById = (userId) => {
  return request.get(`/api/dashboard/users/${userId}`)
}