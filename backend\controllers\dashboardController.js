const { db } = require('../config/database')
const Response = require('../utils/response')

// 获取仪表盘统计数据
const getStats = async (req, res) => {
  try {
    // 从appData集合获取数据
    const appData = await getAppData()
    
    const stats = {
      totalUsers: appData.totalUsers || 0,
      totalLogined: appData.totalLogined || 0,
      totalPost: appData.totalPost || 0,
      totalView: appData.totalView || 0
    }
    
    res.json(Response.success(stats))
  } catch (error) {
    console.error('获取统计数据失败:', error)
    res.status(500).json(Response.error('获取统计数据失败'))
  }
}

// 获取所有用户数据 - 使用与SupplyList相同的分页逻辑
const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      keyword = '',
      searchType = 'nickName'
    } = req.query

    console.log('开始查询用户数据:', { page, pageSize, keyword, searchType })

    // 构建查询条件
    let query = db.collection('users')
    let countQuery = db.collection('users')

    // 如果有搜索关键词，添加搜索条件
    if (keyword && keyword.trim()) {
      const searchField = searchType === 'phoneNumber' ? 'phoneNumber' : 'nickName'
      const searchCondition = {}

      if (searchType === 'phoneNumber') {
        // 手机号精确匹配或部分匹配
        searchCondition[searchField] = db.RegExp({
          regexp: keyword.trim(),
          options: 'i'
        })
      } else {
        // 昵称模糊匹配
        searchCondition[searchField] = db.RegExp({
          regexp: keyword.trim(),
          options: 'i'
        })
      }

      query = query.where(searchCondition)
      countQuery = countQuery.where(searchCondition)
    }

    // 分页查询 - 与SupplyList保持一致
    const skip = (page - 1) * pageSize
    const result = await Promise.race([
      query
        .skip(skip)
        .limit(parseInt(pageSize))
        .orderBy('createTime', 'desc')
        .get(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('查询超时')), 15000)
      )
    ])

    // 获取总数 - 真正的总数查询而不是估算
    const countResult = await Promise.race([
      countQuery.count(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('计数查询超时')), 10000)
      )
    ])

    const total = countResult.total

    console.log(`查询成功，找到 ${result.data.length} 条用户数据，总计 ${total} 条`)

    res.json(Response.success({
      users: result.data,
      pagination: {
        total: total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    }))
  } catch (error) {
    console.error('获取用户列表失败:', error)
    res.status(500).json(Response.error('获取用户列表失败: ' + error.message))
  }
}

// 私有方法：从appData集合获取数据
const getAppData = async () => {
  try {
    console.log('开始从appData集合获取数据...')
    
    const result = await Promise.race([
      db.collection('appData').where({
        _id: '61493796683c0eae01a1f4045d9b0e0b'
      }).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data && result.data.length > 0) {
      const data = result.data[0]
      console.log('成功获取appData:', data)
      return {
        totalUsers: data.totalUsers || 0,
        totalLogined: data.totalLogined || 0,
        totalPost: data.totalPost || 0,
        totalView: data.totalView || 0
      }
    } else {
      console.log('未找到appData，返回默认值')
      return {
        totalUsers: 0,
        totalLogined: 0,
        totalPost: 0,
        totalView: 0
      }
    }
  } catch (error) {
    console.error('获取appData失败:', error.message)
    // 如果获取失败，返回默认值
    return {
      totalUsers: 0,
      totalLogined: 0,
      totalPost: 0,
      totalView: 0
    }
  }
}

// 获取单个用户详情
const getUserById = async (req, res) => {
  try {
    const { userId } = req.params
    console.log('获取用户详情:', userId)
    
    const result = await Promise.race([
      db.collection('users').doc(userId).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data.length > 0) {
      res.json(Response.success(result.data[0]))
    } else {
      res.status(404).json(Response.error('用户不存在'))
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    res.status(500).json(Response.error('获取用户详情失败'))
  }
}

// 更新用户信息
const updateUser = async (req, res) => {
  try {
    const { userId } = req.params
    const updateData = req.body
    
    console.log('更新用户信息:', userId, updateData)
    
    // 过滤不允许更新的字段
    const allowedFields = ['nickName', 'reward', 'province', 'remark']
    const filteredData = {}
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field]
      }
    })
    
    // 添加更新时间
    filteredData.updateTime = new Date().toISOString()
    
    const result = await Promise.race([
      db.collection('users').doc(userId).update(filteredData),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('更新超时')), 8000)
      )
    ])
    
    if (result.updated > 0) {
      // 获取更新后的用户信息
      const updatedUser = await db.collection('users').doc(userId).get()
      res.json(Response.success(updatedUser.data[0], '用户更新成功'))
    } else {
      res.status(404).json(Response.error('用户不存在或无更新'))
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    res.status(500).json(Response.error('更新用户失败'))
  }
}

module.exports = {
  getStats,
  getAllUsers,
  getUserById,
  updateUser
}