const express = require('express')
const cors = require('cors')
require('dotenv').config()

const routes = require('./routes')
const { testConnection } = require('./config/database')

const app = express()
const PORT = process.env.PORT || 3000

// 中间件配置
app.use(cors({
  origin: [
    'http://localhost:5173', 
    'http://localhost:5174', 
    'http://127.0.0.1:5173', 
    'http://127.0.0.1:5174',
    'http://************:5173',
    'http://************:5174'
  ],
  credentials: true
}))

app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`)
  next()
})

// 注册路由
app.use('/', routes)

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: null
  })
})

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: null
  })
})

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 服务器启动成功！`)
  console.log(`📍 本地地址: http://localhost:${PORT}`)
  console.log(`🌐 网络地址: http://************:${PORT}`)
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`☁️ 云开发环境: ${process.env.TCB_ENV_ID}`)
  
  // 测试数据库连接
  try {
    await testConnection()
  } catch (error) {
    console.error('数据库连接测试失败:', error.message)
  }
})

module.exports = app