const express = require('express')
const router = express.Router()

// 导入各模块路由
const userRoutes = require('./users')
const dashboardRoutes = require('./dashboard')
const testRoutes = require('./test')
const geocoderRoutes = require('./geocoder')
const supplyRoutes = require('./supply')
const imageRoutes = require('./images')

// 注册路由
router.use('/api/users', userRoutes)
router.use('/api/dashboard', dashboardRoutes)
router.use('/api/geocoder', geocoderRoutes)
router.use('/api/supply', supplyRoutes)
router.use('/api', imageRoutes)
router.use('/api', testRoutes)

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    code: 200,
    message: '服务正常',
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    }
  })
})

// 测试接口
router.get('/api/test', (req, res) => {
  res.json({
    code: 200,
    message: '测试接口调用成功',
    data: {
      environment: process.env.NODE_ENV,
      tcbEnv: process.env.TCB_ENV_ID
    }
  })
})

module.exports = router