const express = require('express')
const router = express.Router()
const supplyController = require('../controllers/supplyController')

// 获取供应列表（分页、搜索、筛选）
router.get('/list', supplyController.getSupplyList)

// 获取用户的供应信息列表
router.get('/user/:openid', supplyController.getUserSupplies)

// 获取供应详情
router.get('/detail/:id', supplyController.getSupplyDetail)

// 创建新供应
router.post('/create', supplyController.createSupply)

// 更新供应信息
router.put('/:id', supplyController.updateSupply)

// 删除供应信息
router.delete('/:id', supplyController.deleteSupply)

// 批量删除供应
router.post('/batch-delete', supplyController.batchDeleteSupplies)

// 获取供应统计数据
router.get('/stats', supplyController.getSupplyStats)

// 获取分类统计
router.get('/category-stats', supplyController.getCategoryStats)

// 获取热门供应
router.get('/hot', supplyController.getHotSupplies)

module.exports = router